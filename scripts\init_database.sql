-- KingbaseES 数据库初始化脚本

-- 创建源表
CREATE TABLE IF NOT EXISTS public.source_table (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    age INTEGER,
    email VARCHAR(200),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建目标表
CREATE TABLE IF NOT EXISTS public.target_table (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    age INTEGER,
    email VARCHAR(200),
    created_time TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为源表创建更新时间触发器
DROP TRIGGER IF EXISTS update_source_table_updated_time ON public.source_table;
CREATE TRIGGER update_source_table_updated_time
    BEFORE UPDATE ON public.source_table
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 为目标表创建更新时间触发器
DROP TRIGGER IF EXISTS update_target_table_updated_time ON public.target_table;
CREATE TRIGGER update_target_table_updated_time
    BEFORE UPDATE ON public.target_table
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 插入测试数据到源表
INSERT INTO public.source_table (name, age, email) VALUES
('张三', 25, '<EMAIL>'),
('李四', 30, '<EMAIL>'),
('王五', 28, '<EMAIL>'),
('赵六', 35, '<EMAIL>'),
('钱七', 22, '<EMAIL>');

-- 创建索引以提高性能
CREATE INDEX IF NOT EXISTS idx_source_table_created_time ON public.source_table(created_time);
CREATE INDEX IF NOT EXISTS idx_target_table_created_time ON public.target_table(created_time);
CREATE INDEX IF NOT EXISTS idx_source_table_email ON public.source_table(email);
CREATE INDEX IF NOT EXISTS idx_target_table_email ON public.target_table(email);

-- 设置表的副本标识（用于CDC）
ALTER TABLE public.source_table REPLICA IDENTITY FULL;

-- 创建发布（用于逻辑复制）
-- 注意：这需要超级用户权限
-- CREATE PUBLICATION flink_cdc_publication FOR TABLE public.source_table;

-- 显示表结构
\d public.source_table;
\d public.target_table;

-- 显示数据
SELECT 'Source table data:' as info;
SELECT * FROM public.source_table ORDER BY id;

SELECT 'Target table data:' as info;
SELECT * FROM public.target_table ORDER BY id;
