package com.example;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.connector.jdbc.JdbcSink;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.types.Row;

import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Flink CDC作业：同步KingbaseES数据表
 *
 * 此作业从源KingbaseES表读取变更数据，并实时同步到目标表
 */
public class KingbaseESCDCJob {

    private static final Logger LOG = LoggerFactory.getLogger(KingbaseESCDCJob.class);

    public static void main(String[] args) throws Exception {
        // 创建配置管理器
        ConfigManager configManager = new ConfigManager();

        // 验证配置
        if (!configManager.validateConfiguration()) {
            LOG.error("配置验证失败，程序退出");
            System.exit(1);
        }

        // 打印配置信息
        configManager.printConfiguration();

        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 设置检查点
        long checkpointInterval = configManager.getLong("flink.checkpoint.interval", 5000);
        env.enableCheckpointing(checkpointInterval);

        // 设置并行度
        int parallelism = configManager.getInteger("flink.parallelism", 1);
        env.setParallelism(parallelism);

        // 创建Table环境
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);

        // 获取配置
        Configuration config = configManager.getFlinkConfiguration();

        // 方法1：使用DataStream API (暂时注释掉，由于API兼容性问题)
        // runDataStreamJob(env, config, configManager);

        // 方法2：使用Table API
        runTableAPIJob(tableEnv, config, configManager);
    }

    /**
     * 使用DataStream API实现CDC同步 (暂时注释掉，由于API兼容性问题)
     */
    /*
    private static void runDataStreamJob(StreamExecutionEnvironment env, Configuration config, ConfigManager configManager) throws Exception {
        // 创建PostgreSQL CDC源（KingbaseES兼容PostgreSQL协议）
        PostgreSQLSource<String> sourceFunction = PostgreSQLSource.<String>builder()
                .hostname(config.getString("source.hostname", "localhost"))
                .port(config.getInteger("source.port", 54321))
                .database(config.getString("source.database", "test"))
                .schemaList(config.getString("source.schema", "public"))
                .tableList(config.getString("source.schema", "public") + "." + config.getString("source.table", "source_table"))
                .username(config.getString("source.username", "system"))
                .password(config.getString("source.password", "123456"))
                .deserializer(new JsonDebeziumDeserializationSchema())
                .build();

        // 创建数据流
        DataStream<String> cdcStream = env.fromSource(sourceFunction, WatermarkStrategy.noWatermarks(), "KingbaseES CDC Source");

        // 处理CDC数据并转换为Row
        DataStream<Row> processedStream = cdcStream
                .map(new CDCDataProcessor())
                .name("Process CDC Data");

        // 创建JDBC Sink
        processedStream.addSink(
                JdbcSink.sink(
                        buildInsertSQL(configManager),
                        new RowJdbcStatementBuilder(),
                        JdbcExecutionOptions.builder()
                                .withBatchSize(configManager.getInteger("jdbc.batch.size", 1000))
                                .withBatchIntervalMs(configManager.getLong("jdbc.batch.interval", 200))
                                .withMaxRetries(configManager.getInteger("jdbc.max.retries", 5))
                                .build(),
                        new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
                                .withUrl(configManager.getSinkJdbcUrl())
                                .withDriverName("org.postgresql.Driver")
                                .withUsername(configManager.getString("sink.username", "system"))
                                .withPassword(configManager.getString("sink.password", "123456"))
                                .build()
                )
        ).name("KingbaseES Sink");

        // 执行作业
        env.execute("KingbaseES CDC Sync Job");
    }
    */

    /**
     * 使用Table API实现CDC同步
     */
    private static void runTableAPIJob(StreamTableEnvironment tableEnv, Configuration config, ConfigManager configManager) {
        // 创建源表
        String sourceTableDDL = String.format(
                "CREATE TABLE source_table (" +
                "  id BIGINT," +
                "  name STRING," +
                "  age INT," +
                "  email STRING," +
                "  created_time TIMESTAMP(3)," +
                "  PRIMARY KEY (id) NOT ENFORCED" +
                ") WITH (" +
                "  'connector' = 'postgres-cdc'," +
                "  'hostname' = '%s'," +
                "  'port' = '%s'," +
                "  'username' = '%s'," +
                "  'password' = '%s'," +
                "  'database-name' = '%s'," +
                "  'schema-name' = '%s'," +
                "  'table-name' = '%s'" +
                ")",
                configManager.getString("source.hostname", "localhost"),
                configManager.getString("source.port", "54321"),
                configManager.getString("source.username", "system"),
                configManager.getString("source.password", "123456"),
                configManager.getString("source.database", "test"),
                configManager.getString("source.schema", "public"),
                configManager.getString("source.table", "source_table")
        );

        tableEnv.executeSql(sourceTableDDL);

        // 创建目标表
        String sinkTableDDL = String.format(
                "CREATE TABLE target_table (" +
                "  id BIGINT," +
                "  name STRING," +
                "  age INT," +
                "  email STRING," +
                "  created_time TIMESTAMP(3)," +
                "  PRIMARY KEY (id) NOT ENFORCED" +
                ") WITH (" +
                "  'connector' = 'jdbc'," +
                "  'url' = '%s'," +
                "  'table-name' = '%s'," +
                "  'username' = '%s'," +
                "  'password' = '%s'," +
                "  'driver' = 'org.postgresql.Driver'" +
                ")",
                configManager.getSinkJdbcUrl(),
                configManager.getSinkTableName(),
                configManager.getString("sink.username", "system"),
                configManager.getString("sink.password", "123456")
        );

        tableEnv.executeSql(sinkTableDDL);

        // 执行同步
        tableEnv.executeSql("INSERT INTO target_table SELECT * FROM source_table");
    }

    /**
     * 构建JDBC URL
     */
    private static String buildJdbcUrl(Configuration config, String prefix) {
        return String.format("jdbc:kingbase8://%s:%s/%s",
                config.getString(prefix + ".hostname", "localhost"),
                config.getString(prefix + ".port", "54321"),
                config.getString(prefix + ".database", "test"));
    }

    /**
     * 构建插入SQL
     */
    private static String buildInsertSQL(ConfigManager configManager) {
        String tableName = configManager.getSinkTableName();
        return String.format(
                "INSERT INTO %s (id, name, age, email, created_time) VALUES (?, ?, ?, ?, ?) " +
                "ON CONFLICT (id) DO UPDATE SET " +
                "name = EXCLUDED.name, " +
                "age = EXCLUDED.age, " +
                "email = EXCLUDED.email, " +
                "created_time = EXCLUDED.created_time",
                tableName
        );
    }

    /**
     * JDBC Statement Builder
     */
    private static class RowJdbcStatementBuilder implements JdbcStatementBuilder<Row> {
        @Override
        public void accept(PreparedStatement statement, Row row) throws SQLException {
            statement.setLong(1, row.getFieldAs(0));
            statement.setString(2, row.getFieldAs(1));
            statement.setInt(3, row.getFieldAs(2));
            statement.setString(4, row.getFieldAs(3));
            statement.setTimestamp(5, row.getFieldAs(4));
        }
    }
}
