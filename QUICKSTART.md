# KingbaseES CDC 快速开始指南

本指南将帮助您快速设置和运行KingbaseES CDC数据同步项目。

## 前置条件

1. **Java 8+** - 确保已安装Java开发环境
2. **Apache Flink 1.17.1+** - 运行中的Flink集群
3. **KingbaseES 8.6.0+** - 源和目标数据库
4. **Maven 3.6+** - 用于构建项目

## 快速开始步骤

### 1. 克隆或下载项目

确保您已经有了完整的项目文件结构。

### 2. 配置数据库连接

编辑 `src/main/resources/application.properties` 文件：

```properties
# 源数据库配置
source.hostname=your-kingbase-host
source.port=54321
source.database=your-database
source.username=your-username
source.password=your-password
source.schema=public
source.table=source_table

# 目标数据库配置
sink.hostname=your-kingbase-host
sink.port=54321
sink.database=your-database
sink.username=your-username
sink.password=your-password
sink.schema=public
sink.table=target_table
```

### 3. 初始化数据库

在KingbaseES中执行初始化脚本：

```bash
# 连接到KingbaseES数据库
psql -h localhost -p 54321 -U system -d test

# 执行初始化脚本
\i scripts/init_database.sql
```

### 4. 构建项目

```bash
mvn clean package -DskipTests
```

### 5. 启动Flink集群

确保Flink集群正在运行：

```bash
# 启动Flink集群（如果尚未启动）
$FLINK_HOME/bin/start-cluster.sh

# 检查集群状态
curl http://localhost:8081/overview
```

### 6. 部署CDC作业

#### 方式一：使用部署脚本（推荐）

**Linux/Mac:**
```bash
./scripts/deploy.sh deploy
```

**Windows:**
```cmd
scripts\deploy.bat
```

#### 方式二：手动部署

```bash
$FLINK_HOME/bin/flink run \
    --jobmanager localhost:8081 \
    --class com.example.KingbaseESCDCJob \
    --detached \
    target/flink-kingbase-cdc-1.0.0.jar
```

### 7. 验证同步

#### 插入测试数据

```bash
# 插入10条测试数据
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator insert 10
```

#### 检查同步状态

```bash
# 检查同步状态
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor check
```

#### 持续监控

```bash
# 每5秒监控一次同步状态
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor monitor 5
```

## 使用交互式脚本

为了更方便的操作，我们提供了交互式脚本：

**Linux/Mac:**
```bash
./scripts/example_usage.sh
```

**Windows:**
```cmd
scripts\example_usage.bat
```

这个脚本提供了一个菜单界面，包含所有常用操作。

## 监控和管理

### 查看Flink Web UI

访问 `http://localhost:8081` 查看：
- 作业运行状态
- 处理速度和延迟
- 检查点状态
- 错误日志

### 查看应用日志

日志文件位置：`logs/flink-cdc.log`

### 停止作业

```bash
# 获取作业ID
$FLINK_HOME/bin/flink list

# 停止作业
$FLINK_HOME/bin/flink cancel <job-id>
```

## 测试场景

### 1. 基本同步测试

```bash
# 1. 插入数据到源表
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator insert 5

# 2. 检查目标表是否同步
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor check
```

### 2. 更新数据测试

```bash
# 更新源表中的随机数据
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator update

# 检查同步状态
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor check
```

### 3. 连续数据生成测试

```bash
# 每3秒生成一次数据变更
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator continuous 3

# 在另一个终端监控同步状态
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor monitor 5
```

## 故障排除

### 常见问题

1. **连接数据库失败**
   - 检查 `application.properties` 中的数据库配置
   - 确认数据库服务正在运行
   - 验证网络连接

2. **CDC无法启动**
   - 确认KingbaseES已启用逻辑复制
   - 检查用户权限
   - 验证表的REPLICA IDENTITY设置

3. **作业提交失败**
   - 检查Flink集群状态
   - 验证JAR文件路径
   - 查看JobManager日志

### 调试步骤

1. **检查配置**
   ```bash
   # 验证配置是否正确
   java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.ConfigManager
   ```

2. **测试数据库连接**
   ```bash
   # 测试源数据库连接
   psql -h localhost -p 54321 -U system -d test -c "SELECT COUNT(*) FROM public.source_table;"
   ```

3. **查看详细日志**
   ```bash
   # 查看应用日志
   tail -f logs/flink-cdc.log
   
   # 查看Flink日志
   tail -f $FLINK_HOME/log/flink-*-jobmanager-*.log
   ```

## 性能调优

### 1. 调整批处理参数

在 `application.properties` 中：

```properties
# 增加批处理大小以提高吞吐量
jdbc.batch.size=2000
jdbc.batch.interval=100

# 调整检查点间隔
flink.checkpoint.interval=10000
```

### 2. 调整并行度

```properties
# 增加并行度（根据集群资源调整）
flink.parallelism=2
```

### 3. 数据库优化

- 为经常查询的字段创建索引
- 调整数据库连接池大小
- 优化数据库配置参数

## 下一步

- 根据实际需求修改数据处理逻辑
- 添加更多的监控指标
- 实现自定义的数据转换规则
- 集成到CI/CD流水线

## 支持

如果遇到问题，请检查：
1. 项目README.md文档
2. Flink官方文档
3. KingbaseES官方文档
4. 项目日志文件
