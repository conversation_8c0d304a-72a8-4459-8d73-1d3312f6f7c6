package com.example;

import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 配置管理器
 *
 * 负责加载和管理应用程序配置
 */
public class ConfigManager {

    private static final Logger LOG = LoggerFactory.getLogger(ConfigManager.class);
    private static final String CONFIG_FILE = "application.properties";

    private final Properties properties;
    private final Configuration flinkConfig;

    public ConfigManager() {
        this.properties = new Properties();
        this.flinkConfig = new Configuration();
        loadConfiguration();
    }

    /**
     * 加载配置文件
     */
    private void loadConfiguration() {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (inputStream == null) {
                LOG.warn("配置文件 {} 不存在，使用默认配置", CONFIG_FILE);
                setDefaultConfiguration();
                return;
            }

            properties.load(inputStream);
            LOG.info("成功加载配置文件: {}", CONFIG_FILE);

            // 将Properties转换为Flink Configuration
            for (String key : properties.stringPropertyNames()) {
                flinkConfig.setString(key, properties.getProperty(key));
            }

        } catch (IOException e) {
            LOG.error("加载配置文件失败: {}", CONFIG_FILE, e);
            setDefaultConfiguration();
        }
    }

    /**
     * 设置默认配置
     */
    private void setDefaultConfiguration() {
        // 源数据库配置
        flinkConfig.setString("source.hostname", "localhost");
        flinkConfig.setString("source.port", "54321");
        flinkConfig.setString("source.database", "test");
        flinkConfig.setString("source.username", "system");
        flinkConfig.setString("source.password", "123456");
        flinkConfig.setString("source.schema", "public");
        flinkConfig.setString("source.table", "source_table");

        // 目标数据库配置
        flinkConfig.setString("sink.hostname", "localhost");
        flinkConfig.setString("sink.port", "54321");
        flinkConfig.setString("sink.database", "test");
        flinkConfig.setString("sink.username", "system");
        flinkConfig.setString("sink.password", "123456");
        flinkConfig.setString("sink.schema", "public");
        flinkConfig.setString("sink.table", "target_table");

        // Flink作业配置
        flinkConfig.setString("flink.checkpoint.interval", "5000");
        flinkConfig.setString("flink.parallelism", "1");

        // JDBC配置
        flinkConfig.setString("jdbc.batch.size", "1000");
        flinkConfig.setString("jdbc.batch.interval", "200");
        flinkConfig.setString("jdbc.max.retries", "5");

        LOG.info("使用默认配置");
    }

    /**
     * 获取Flink配置对象
     */
    public Configuration getFlinkConfiguration() {
        return flinkConfig;
    }

    /**
     * 获取字符串配置值
     */
    public String getString(String key, String defaultValue) {
        return flinkConfig.getString(key, defaultValue);
    }

    /**
     * 获取整数配置值
     */
    public int getInteger(String key, int defaultValue) {
        return flinkConfig.getInteger(key, defaultValue);
    }

    /**
     * 获取长整数配置值
     */
    public long getLong(String key, long defaultValue) {
        return flinkConfig.getLong(key, defaultValue);
    }

    /**
     * 获取布尔配置值
     */
    public boolean getBoolean(String key, boolean defaultValue) {
        return flinkConfig.getBoolean(key, defaultValue);
    }

    /**
     * 构建源数据库JDBC URL
     */
    public String getSourceJdbcUrl() {
        return String.format("jdbc:postgresql://%s:%s/%s",
                getString("source.hostname", "localhost"),
                getString("source.port", "54321"),
                getString("source.database", "test"));
    }

    /**
     * 构建目标数据库JDBC URL
     */
    public String getSinkJdbcUrl() {
        return String.format("jdbc:postgresql://%s:%s/%s",
                getString("sink.hostname", "localhost"),
                getString("sink.port", "54321"),
                getString("sink.database", "test"));
    }

    /**
     * 获取源表全名
     */
    public String getSourceTableName() {
        return getString("source.schema", "public") + "." + getString("source.table", "source_table");
    }

    /**
     * 获取目标表全名
     */
    public String getSinkTableName() {
        return getString("sink.schema", "public") + "." + getString("sink.table", "target_table");
    }

    /**
     * 打印配置信息（隐藏敏感信息）
     */
    public void printConfiguration() {
        LOG.info("=== 配置信息 ===");
        LOG.info("源数据库: {}:{}/{}",
                getString("source.hostname", "localhost"),
                getString("source.port", "54321"),
                getString("source.database", "test"));
        LOG.info("源表: {}", getSourceTableName());
        LOG.info("目标数据库: {}:{}/{}",
                getString("sink.hostname", "localhost"),
                getString("sink.port", "54321"),
                getString("sink.database", "test"));
        LOG.info("目标表: {}", getSinkTableName());
        LOG.info("检查点间隔: {}ms", getString("flink.checkpoint.interval", "5000"));
        LOG.info("并行度: {}", getString("flink.parallelism", "1"));
        LOG.info("JDBC批处理大小: {}", getString("jdbc.batch.size", "1000"));
        LOG.info("===============");
    }

    /**
     * 验证配置的有效性
     */
    public boolean validateConfiguration() {
        boolean isValid = true;

        // 验证必需的配置项
        String[] requiredKeys = {
                "source.hostname", "source.database", "source.username", "source.password",
                "sink.hostname", "sink.database", "sink.username", "sink.password"
        };

        for (String key : requiredKeys) {
            String value = getString(key, null);
            if (value == null || value.trim().isEmpty()) {
                LOG.error("必需的配置项缺失或为空: {}", key);
                isValid = false;
            }
        }

        // 验证端口号
        try {
            int sourcePort = getInteger("source.port", 54321);
            int sinkPort = getInteger("sink.port", 54321);
            if (sourcePort <= 0 || sourcePort > 65535 || sinkPort <= 0 || sinkPort > 65535) {
                LOG.error("端口号无效: source.port={}, sink.port={}", sourcePort, sinkPort);
                isValid = false;
            }
        } catch (Exception e) {
            LOG.error("端口号配置错误", e);
            isValid = false;
        }

        // 验证数值配置
        try {
            long checkpointInterval = getLong("flink.checkpoint.interval", 5000);
            int parallelism = getInteger("flink.parallelism", 1);
            int batchSize = getInteger("jdbc.batch.size", 1000);

            if (checkpointInterval <= 0) {
                LOG.error("检查点间隔必须大于0: {}", checkpointInterval);
                isValid = false;
            }

            if (parallelism <= 0) {
                LOG.error("并行度必须大于0: {}", parallelism);
                isValid = false;
            }

            if (batchSize <= 0) {
                LOG.error("批处理大小必须大于0: {}", batchSize);
                isValid = false;
            }

        } catch (Exception e) {
            LOG.error("数值配置验证失败", e);
            isValid = false;
        }

        if (isValid) {
            LOG.info("配置验证通过");
        } else {
            LOG.error("配置验证失败");
        }

        return isValid;
    }
}
