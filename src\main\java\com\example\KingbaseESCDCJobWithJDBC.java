package com.example;

import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * KingbaseES CDC作业 - 使用自定义JDBC写入
 *
 * 此版本使用DataStream API和自定义JDBC Sink来避免依赖Flink集群的JDBC连接器
 */
public class KingbaseESCDCJobWithJDBC {

    private static final Logger LOG = LoggerFactory.getLogger(KingbaseESCDCJobWithJDBC.class);

    public static void main(String[] args) throws Exception {
        // 创建配置管理器
        ConfigManager configManager = new ConfigManager();

        // 验证配置
        if (!configManager.validateConfiguration()) {
            LOG.error("配置验证失败，程序退出");
            System.exit(1);
        }

        // 打印配置信息
        configManager.printConfiguration();

        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 设置检查点
        long checkpointInterval = configManager.getLong("flink.checkpoint.interval", 5000);
        env.enableCheckpointing(checkpointInterval);

        // 设置并行度
        int parallelism = configManager.getInteger("flink.parallelism", 1);
        env.setParallelism(parallelism);

        // 创建Table环境
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);

        // 获取配置
        Configuration config = configManager.getFlinkConfiguration();

        // 使用Table API创建CDC源，然后转换为DataStream进行自定义处理
        runHybridJob(env, tableEnv, configManager);
    }

    /**
     * 混合方法：使用Table API创建CDC源，DataStream API处理数据
     */
    private static void runHybridJob(StreamExecutionEnvironment env, StreamTableEnvironment tableEnv, ConfigManager configManager) throws Exception {

        // 创建CDC源表
        String sourceTableDDL = String.format(
                "CREATE TABLE source_table (" +
                "  id BIGINT," +
                "  name STRING," +
                "  age INT," +
                "  email STRING," +
                "  created_time TIMESTAMP(3)," +
                "  PRIMARY KEY (id) NOT ENFORCED" +
                ") WITH (" +
                "  'connector' = 'postgres-cdc'," +
                "  'hostname' = '%s'," +
                "  'port' = '%s'," +
                "  'username' = '%s'," +
                "  'password' = '%s'," +
                "  'database-name' = '%s'," +
                "  'schema-name' = '%s'," +
                "  'table-name' = '%s'," +
                "  'slot.name' = '%s'," +
                "  'decoding.plugin.name' = 'pgoutput'" +
                ")",
                configManager.getString("source.hostname", "localhost"),
                configManager.getString("source.port", "54321"),
                configManager.getString("source.username", "system"),
                configManager.getString("source.password", "123456"),
                configManager.getString("source.database", "test"),
                configManager.getString("source.schema", "public"),
                configManager.getString("source.table", "source_table"),
                configManager.getString("cdc.slot.name", "flink_cdc_slot")
        );

        tableEnv.executeSql(sourceTableDDL);

        // 将Table转换为DataStream
        org.apache.flink.table.api.Table sourceTable = tableEnv.from("source_table");
        org.apache.flink.streaming.api.datastream.DataStream<Row> dataStream = tableEnv.toChangelogStream(sourceTable);

        // 处理数据并写入目标数据库
        dataStream
                .map(new CustomJDBCProcessor(configManager))
                .name("Custom JDBC Writer");

        LOG.info("开始执行KingbaseES CDC同步作业（使用自定义JDBC）...");

        // 执行作业
        env.execute("KingbaseES CDC Sync Job with Custom JDBC");
    }
}
